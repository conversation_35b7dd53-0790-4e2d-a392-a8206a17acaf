# Zygo AI IDE v6.2 开发文档

## 项目概述

Zygo AI IDE 是一个基于人工智能的物联网集成开发环境，专门用于多设备物联网系统的智能化开发。该项目结合了LangChain、LangGraph和Flask技术栈，提供从需求分析到代码生成、部署验证的全流程自动化开发体验。

### 核心特性

- 🤖 **AI驱动的需求分析**：自动将自然语言需求转换为结构化的多设备系统架构
- 🔧 **智能代码生成**：基于LangGraph工作流自动生成嵌入式固件代码
- 🌐 **多设备协作**：支持ESP32等嵌入式设备的MQTT通信和云平台集成
- 📊 **实时监控**：提供设备状态监控、日志流和OTA更新功能
- 🎯 **模块化架构**：采用微服务架构，便于扩展和维护

## 技术栈

### 后端技术
- **Python 3.11+**
- **Flask** - Web框架
- **SQLAlchemy** - ORM数据库操作
- **Flask-Migrate** - 数据库迁移
- **LangChain** - AI应用开发框架
- **LangGraph** - 工作流编排
- **OpenAI API** - 大语言模型服务

### 前端技术
- **HTML5/CSS3/JavaScript**
- **Monaco Editor** - 代码编辑器
- **WebSocket** - 实时通信

### 数据库
- **SQLite** (开发环境)
- **PostgreSQL/MySQL** (生产环境)

### 物联网技术
- **MQTT** - 设备通信协议
- **ESP32/Arduino** - 嵌入式开发平台
- **涂鸦云平台** - IoT云服务集成

## 项目结构

```
zygo/
├── app/                          # Flask应用主目录
│   ├── __init__.py              # 应用工厂函数
│   ├── models.py                # 数据模型定义
│   ├── api/                     # REST API路由
│   │   ├── auth_routes.py       # 用户认证API
│   │   ├── device_routes.py     # 设备管理API
│   │   ├── project_routes.py    # 项目管理API
│   │   ├── workflow_routes.py   # 工作流API
│   │   ├── user_routes.py       # 用户配置API
│   │   └── log_stream_routes.py # 日志流API
│   ├── services/                # 业务逻辑服务
│   │   ├── auth_service.py      # 认证服务
│   │   ├── device_service.py    # 设备管理服务
│   │   ├── project_analyzer_service.py  # 项目分析服务
│   │   ├── syntax_analyzer_service.py   # 语法分析服务
│   │   ├── user_service.py      # 用户服务
│   │   └── workflow_service.py  # 工作流服务
│   ├── langgraph_def/           # LangGraph工作流定义
│   │   ├── agent_state.py       # 状态模型
│   │   └── graph_builder.py     # 图构建器
│   ├── templates/               # HTML模板
│   │   └── index.html          # 主页面
│   ├── static/                  # 静态资源
│   └── temp_workspaces/         # 临时工作空间
├── API_Package/                 # 设备API规范库
│   ├── DHT11_API_Package.json   # DHT11传感器API
│   ├── BMP280_API_Package.json  # BMP280传感器API
│   └── ...                     # 其他设备API规范
├── config.py                    # 配置文件
├── run.py                       # 应用启动文件
├── example.py                   # 示例脚本
└── create_handover_file.py      # 交接文档生成器
```

## 快速开始

### 环境要求

- Python 3.11 或更高版本
- pip 包管理器
- Git (可选，用于版本控制)

### 安装步骤

1. **克隆项目**
```bash
git clone <repository-url>
cd zygo
```

2. **创建虚拟环境**
```bash
python -m venv venv
# Windows
venv\Scripts\activate
# Linux/Mac
source venv/bin/activate
```

3. **安装依赖**
```bash
pip install flask flask-sqlalchemy flask-migrate
pip install langchain langchain-openai langgraph
pip install thefuzz paho-mqtt
pip install requests websockets
```

4. **配置环境变量**
```bash
# Windows
set FLASK_CONFIG=development
set SECRET_KEY=your-secret-key-here

# Linux/Mac
export FLASK_CONFIG=development
export SECRET_KEY=your-secret-key-here
```

5. **初始化数据库**
```bash
python run.py init-db
```

6. **启动应用**
```bash
python run.py
```

访问 http://localhost:5000 查看应用。

## 核心功能模块

### 1. 用户认证系统
- 用户注册/登录
- JWT Token认证
- 权限管理

### 2. 设备管理
- 设备注册和配置
- 设备状态监控
- 设备分组管理

### 3. 项目管理
- 项目创建和配置
- 需求分析和架构设计
- 代码生成和版本管理

### 4. 工作流引擎
- 基于LangGraph的智能工作流
- 多阶段代码生成流程
- 自动化测试和部署

### 5. 实时监控
- 设备日志流
- 系统状态监控
- 错误报警机制

## API 接口文档

### 认证接口

#### 用户注册
```http
POST /api/v1/auth/register
Content-Type: application/json

{
  "username": "your_username",
  "password": "your_password"
}
```

#### 用户登录
```http
POST /api/v1/auth/login
Content-Type: application/json

{
  "username": "your_username", 
  "password": "your_password"
}
```

### 设备管理接口

#### 获取设备列表
```http
GET /api/v1/devices
Authorization: Bearer <token>
```

#### 注册新设备
```http
POST /api/v1/devices
Authorization: Bearer <token>
Content-Type: application/json

{
  "nickname": "设备昵称",
  "board_model": "ESP32",
  "description": "设备描述"
}
```

### 项目管理接口

#### 创建项目
```http
POST /api/v1/projects
Authorization: Bearer <token>
Content-Type: application/json

{
  "name": "项目名称",
  "config_json": {...}
}
```

#### 分析项目需求
```http
POST /api/v1/projects/analyze
Authorization: Bearer <token>
Content-Type: application/json

{
  "raw_text": "用自然语言描述的项目需求"
}
```

### 工作流接口

#### 启动工作流
```http
POST /api/v1/workflows
Authorization: Bearer <token>
Content-Type: application/json

{
  "project_name": "项目名称",
  "project_description": "项目描述",
  "device_tasks": [...],
  "communication_plan": {...}
}
```

#### 获取工作流状态
```http
GET /api/v1/workflows/<workflow_id>
Authorization: Bearer <token>
```

## 开发指南

### 代码规范

1. **Python代码规范**
   - 遵循PEP 8编码规范
   - 使用类型注解
   - 编写文档字符串

2. **API设计规范**
   - RESTful API设计
   - 统一的错误处理
   - 标准的HTTP状态码

3. **数据库设计规范**
   - 使用SQLAlchemy ORM
   - 合理的表结构设计
   - 数据库迁移管理

### 添加新功能

1. **添加新的API端点**
   - 在相应的routes文件中添加路由
   - 在services中实现业务逻辑
   - 更新API文档

2. **添加新的数据模型**
   - 在models.py中定义模型
   - 创建数据库迁移
   - 更新相关服务

3. **扩展工作流节点**
   - 在graph_builder.py中添加新节点
   - 更新状态模型
   - 测试工作流逻辑

### 测试指南

1. **单元测试**
```bash
python -m pytest tests/
```

2. **API测试**
```bash
python api_test_script.py
```

3. **工作流测试**
```bash
python workflow_test_script.py
```

## 部署指南

### 开发环境部署
- 使用SQLite数据库
- 启用调试模式
- 热重载功能

### 生产环境部署
- 使用PostgreSQL/MySQL数据库
- 配置反向代理(Nginx)
- 启用HTTPS
- 配置日志系统
- 设置监控告警

### Docker部署
```dockerfile
FROM python:3.11-slim
WORKDIR /app
COPY requirements.txt .
RUN pip install -r requirements.txt
COPY . .
EXPOSE 5000
CMD ["python", "run.py"]
```

## 故障排除

### 常见问题

1. **数据库连接失败**
   - 检查数据库配置
   - 确认数据库服务状态
   - 验证连接字符串

2. **AI模型调用失败**
   - 检查API密钥配置
   - 验证网络连接
   - 查看模型服务状态

3. **设备连接问题**
   - 检查MQTT服务器状态
   - 验证设备网络配置
   - 查看设备日志

### 日志分析
- 应用日志位置：`logs/app.log`
- 错误日志位置：`logs/error.log`
- 设备日志：通过MQTT主题获取

## 贡献指南

1. Fork项目仓库
2. 创建功能分支
3. 提交代码更改
4. 创建Pull Request
5. 代码审查和合并

## 许可证

本项目采用MIT许可证，详见LICENSE文件。

## 联系方式

- 项目维护者：[维护者姓名]
- 邮箱：[联系邮箱]
- 问题反馈：[GitHub Issues链接]
